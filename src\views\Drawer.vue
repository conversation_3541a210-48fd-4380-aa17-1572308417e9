<!--
 * @Author: tianrui.li <EMAIL>
 * @Date: 2023-02-22 09:58:06
 * @LastEditors: tianrui.li <EMAIL>
 * @LastEditTime: 2023-06-01 16:21:57
 * @FilePath: \vueDemo\src\views\pageTwo.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div>
        <el-drawer title="title" :visible.sync="opendrawer.open">
            <span>{{ opendrawer.name }}</span>
            <span>{{ opendrawer.sex }}</span>
            <span>{{ opendrawer.age }}</span>
            <span>{{ opendrawer.birth }}</span>
            <span>{{ opendrawer.addr }}</span>
        </el-drawer>
    </div>
</template>
<script>
export default {
  props: {
    opendrawer: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      tableData: []
    }
  }
}
</script>
