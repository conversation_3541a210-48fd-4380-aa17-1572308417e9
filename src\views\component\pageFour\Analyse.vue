<template>
  <div>
    <el-row :gutter="5">
        <el-col :span="4" :xs="24">
            <el-select
                v-model="queryParams.viewName"
                style="width: 100%;"
                placeholder="视图名称"
            >
                <el-option
                    v-for="item in viewData"
                    :key="item.index"
                    :label="item.viewName"
                    :value="item.title"
                ></el-option>
            </el-select>
        </el-col>
        <el-col :span="11">
            <el-input 
                v-model="queryParams.keyword"
                placeholder="查询条件"
                clearable
            ></el-input>
        </el-col>
        <el-col :span="6">
            <el-date-picker
                style="width: 100%;"
                v-model="queryParams.timestamp"
                type="datetimerange"
                :picker-options="pickerOptions"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                align="right"
                >
            </el-date-picker>
        </el-col>
        <el-col :span="3" :xs="24">
            <div style="float: right;">
                <el-button type="primary" icon="el-icon-search">搜索</el-button>
                <el-button icon="el-icon=refresh">重置</el-button>
            </div>
        </el-col>
    </el-row>
    <!-- <el-row :gutter="10">
        <el-col :span="24" :xs="24">
            <div style="display: flex;">
                <div class="view-name-item">
                    <el-select
                        v-model="queryParams.viewName"
                        placeholder="视图名称"
                    >
                        <el-option
                            v-for="item in viewData"
                            :key="item.index"
                            :label="item.viewName"
                            :value="item.title"
                        ></el-option>
                    </el-select>
                </div>
                <div style="flex: 1 1 0%">
                    <div class="search-group">
                        <div class="search-item">
                            <el-input 
                                v-model="queryParams.keyword"
                                placeholder="查询条件"
                                clearable
                            ></el-input>
                        </div>
                        <div class="time-item">
                            <el-date-picker
                                v-model="queryParams.timestamp"
                                type="datetimerange"
                                :picker-options="pickerOptions"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                                align="right"
                                >
                            </el-date-picker>
                        </div>
                        <div class="button-item">
                            <el-button
                                type="primary"
                                icon="el-icon-search"
                            >搜索</el-button>
                        </div>
                        <div class="button-item">
                            <el-button icon="el-icon=refresh">重置</el-button>
                        </div>
                    </div>
                </div>
            </div>
        </el-col>
    </el-row> -->
    <el-row :gutter="20" style="margin-top: 5px;">
        <el-col :span="24" :xs="24">
            <el-button type="text" icon="el-icon-plus">添加条件</el-button>
        </el-col>
    </el-row>
    <el-row :gutter="20" style="margin-top: 5px;">
        <el-col :span="4" :xs="24">
            <div class="head-container">
                <el-input
                    v-model="fieldName"
                    placeholder="请输入字段名称"
                    clearable
                    size="small"
                    prefix-icon="el-icon-search"
                    style="margin-bottom: 20px;"
                ></el-input>
            </div>
            <div class="head-container">
                <el-tree
                    :data="fieldData"
                    :props="defaultProps"
                    :expand-on-click-node="false"
                    :filter-node-method="filterNode"
                    ref="tree"
                    node-key="id"
                    default-expand-all
                    highlight-current
                ></el-tree>
            </div>
        </el-col>
        <el-col :span="20" :xs="24">
            <el-card shadow="never">
                <el-table :data="tableData">
                    <el-table-column label="时间"></el-table-column>
                    <el-table-column label="消息"></el-table-column>
                </el-table>
            </el-card>
        </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
    data(){
        return{
            queryParams:{},
            viewData:[
                {
                    viewName:'1',
                    title:'title'
                },
                {
                    viewName:'2',
                    title:'title'
                }
            ],
            pickerOptions: {
                shortcuts: [
                    {
                        text: "最近一周",
                        onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit("pick", [start, end]);
                        },
                    },
                    {
                        text: "最近一个月",
                        onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                        picker.$emit("pick", [start, end]);
                        },
                    },
                    {
                        text: "最近三个月",
                        onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                        picker.$emit("pick", [start, end]);
                        },
                    },
                ],
            },
            fieldData: [
                {
                id: 1,
                label: "选定字段",
                children: [],
                },
                {
                id: 2,
                label: "可用字段",
                children: [],
                },
            ],
            defaultProps: {
                children: "children",
                label: "label",
            },
            fieldName:undefined,
            tableData:[]
        }
    },
    methods:{
        // 筛选节点
        filterNode(value, data) {
            if (!value) return true;
            return data.label.indexOf(value) !== -1;
        },
    }
}
</script>
<style scoped>
.view-name-item {
  display: flex;
  flex-direction: column;
}
.search-group {
  margin-left: 5px;
  align-items: stretch;
  flex-direction: row;
  justify-content: flex-end;

  display: flex;
  align-items: stretch;
  flex-grow: 1;
}
.search-item {
    flex-grow: 1;
    flex-basis: 0%;
    display: flex;
    flex-direction: column;
  }
  .time-item {
    flex-grow: 0;
    flex-basis: auto;
    display: flex;
    flex-direction: column;
    margin-left: 5px;
  }
  .button-item {
    flex-grow: 0;
    flex-basis: auto;
    display: flex;
    flex-direction: column;
    margin-left: 5px;
  }
</style>
