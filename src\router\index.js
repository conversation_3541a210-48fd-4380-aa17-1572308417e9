/*
 * @Author: tianrui.li <EMAIL>
 * @Date: 2023-02-20 11:49:35
 * @LastEditors: tianrui.li <EMAIL>
 * @LastEditTime: 2025-09-22 10:12:27
 * @FilePath: \vueDemo\src\router\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import Vue from 'vue'
import Router from 'vue-router'
// import HelloWorld from '@/components/HelloWorld'
import Main from '@/views/Main.vue'
import Home from '@/views/Home.vue'
import User from '@/views/User.vue'
import Mall from '@/views/Mall.vue'
import pageOne from '@/views/pageOne.vue'
import pageTwo from '@/views/pageTwo.vue'
import pageThree from '@/views/pageThree.vue'
import pageFour from '@/views/pageFour.vue'
import pageFive from '@/views/pageFive.vue'
import Login from '@/views/Login.vue'
Vue.use(Router)

export default new Router({
  routes: [
    {
      path: '/',
      // name: 'HelloWorld',
      component: Main,
      // 重定向，当路径是一斜杠的时候定位到home
      redirect: '/home',
      children: [
        // 首页
        {path: 'home', name: 'home', component: Home},
        // 用户管理
        {path: 'user', name: 'user', component: User},
        // 商品管理
        {path: 'mall', name: 'mall', component: Mall},
        // 页面1
        {path: 'page1', name: 'page1', component: pageOne},
        // 页面2
        {path: 'page2', name: 'page2', component: pageTwo},
        // 页面3
        {path: 'page3', name: 'page3', component: pageThree},
        // 页面4
        {path: 'page4', name: 'page4', component: pageFour},
        // 页面5
        {path: 'page5', name: 'page5', component: pageFive}
      ]
    },
    {
      path: '/login',
      name: 'login',
      component: Login
    }
  ]
})
