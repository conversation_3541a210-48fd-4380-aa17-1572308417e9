/*
 * @Author: tianrui.li <EMAIL>
 * @Date: 2023-03-06 15:57:00
 * @LastEditors: tianrui.li <EMAIL>
 * @LastEditTime: 2023-05-09 17:00:44
 * @FilePath: \vueDemo\src\api\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import http from '../utils/request'

// 定义接口
// 请求首页数据
export const getData = () => {
// 返回一个promise对象
  return http.get('/home/<USER>')
}

export const getUser = (params) => {
//  返回用户列表
  return http.get('/user/getUser', params)
}

export const addUser = (data) => {
  return http.post('/user/add', data)
}

export const editUser = (data) => {
  return http.post('/user/edit', data)
}

export const delUser = (data) => {
  return http.post('/user/del', data)
}

export const getMenu = (data) => {
  return http.post('/permission/getMenu', data)
}
