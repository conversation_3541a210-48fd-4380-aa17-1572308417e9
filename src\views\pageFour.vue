<!--
 * @Author: tianrui.li <EMAIL>
 * @Date: 2023-06-27 17:17:27
 * @LastEditors: tianrui.li <EMAIL>
 * @LastEditTime: 2023-07-14 16:07:13
 * @FilePath: \vueDemo\src\views\pageFour.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div>
        <el-tabs>
            <el-tab-pane>
                <span slot="label">告警看板</span>
                <el-row :gutter="10">
                    <el-col :span="18">
                        <el-card style="margin-bottom: 10px;">
                            <div slot="header">
                                <span>告警看板</span>
                            </div>
                            <div style="display:flex">
                                <div style="width: 33%;margin-right: 10px;height:400px;background-color:#f6f6f6"
                                    v-for="item in list" :key="item.index">
                                    <div v-for="obj in item" :key="obj.key">
                                        <el-card
                                            :style="`border-left: 4px solid ${getColor(obj.severity)};margin-bottom:10px`">
                                            <div>
                                                <el-link :underline="false">{{ obj.description }}</el-link>
                                                <el-dropdown trigger="click" style="float: right;">
                                                    <span>
                                                        <i class="el-icon-more" style="transform: rotate(90deg);"></i>
                                                    </span>
                                                    <el-dropdown-menu slot="dropdown">
                                                        <el-dropdown-item icon="el-icon-tickets">查看详情</el-dropdown-item>
                                                        <el-dropdown-item icon="el-icon-delete-solid">删除</el-dropdown-item>
                                                    </el-dropdown-menu>
                                                </el-dropdown>
                                            </div>
                                            <el-tag type="info" size="mini" style="margin-top:10px">{{ obj.time }}</el-tag>
                                            <el-tag type="info" size="mini" style="margin-top:10px">{{ obj.tag }}</el-tag>
                                            <div style="margin-top:10px;word-wrap: break-word;word-break:break-all">
                                                <el-tag type="info" size="mini">{{ obj.value }}</el-tag>
                                            </div>
                                        </el-card>
                                    </div>
                                </div>
                            </div>
                        </el-card>
                        <el-row :gutter="10" class="row">
                            <el-col :span="16">
                                <el-card class="card">
                                    <span style="font-weight: 700;">相关信息</span>
                                    <el-tooltip placement="top">
                                        <span slot="content">相关信息</span>
                                        <span><i class="el-icon-info"></i></span>
                                    </el-tooltip>
                                    <el-divider></el-divider>
                                    <div v-for="item in descriptionList" :key="item.title">
                                        <el-descriptions :title="item.title" :column="2">
                                            <el-descriptions-item label="分派策略">测试</el-descriptions-item>
                                            <el-descriptions-item label="关联应用">产出</el-descriptions-item>
                                            <el-descriptions-item label="分派策略">测试</el-descriptions-item>
                                            <el-descriptions-item label="关联应用">测试</el-descriptions-item>
                                            <el-descriptions-item label="分派策略">测试</el-descriptions-item>
                                            <el-descriptions-item label="关联应用">测试</el-descriptions-item>
                                        </el-descriptions>
                                        <el-divider></el-divider>
                                    </div>
                                </el-card>
                            </el-col>
                            <el-col :span="8">
                                <el-card class="card">
                                    <span style="font-weight: 700;">通知方式</span>
                                    <span style="float:right"><el-button type="text" size="mini">编辑</el-button></span>
                                    <el-divider></el-divider>
                                    <el-radio-group v-model="radio1" size="mini" fill="#f6f6f6" text-color="#3e80c6">
                                        <el-radio-button label="发生时"></el-radio-button>
                                        <el-radio-button label="认领时"></el-radio-button>
                                        <el-radio-button label="关闭时"></el-radio-button>
                                    </el-radio-group>
                                    <div style="margin-top:20px">
                                        <div style="font-size: 13px;">01 任何时间/严重告警/立刻
                                            <svg class="icon" aria-hidden="true"
                                                style="width: 25px;height:25px;float:right">
                                                <use xlink:href="#icon-mail"></use>
                                            </svg>
                                        </div>
                                        <el-divider></el-divider>
                                    </div>
                                </el-card>
                            </el-col>
                        </el-row>
                    </el-col>
                    <el-col :span="6">
                        <el-card style="margin-bottom: 10px;">
                            <span>统计分析</span>
                            <el-divider></el-divider>
                            <el-col :span="24">
                                <StatisticAnalysis />
                            </el-col>
                        </el-card>
                        <el-card>
                            值班信息
                            <span style="float: right;">
                                <el-button type="text" size="mini">更多</el-button>
                            </span>
                            <el-divider></el-divider>
                            <div style="transform: translate(40%, 0%);margin-bottom:10px">当前值班信息</div>
                            <el-card style="margin-bottom: 10px;">
                                <el-col :span="11" style="transform: translate(40%, 0%);">
                                    <div style="font-size:12px;color: rgb(157, 162, 167);">开始</div>
                                    <div style="font-size:18px;font-weight:600;color: rgb(5, 13, 20);">周二</div>
                                    <div style="font-size:18px;font-weight:600;color: rgb(5, 13, 20);">05月10日</div>
                                    <div style="font-size:12px;color: rgb(157, 162, 167);">08:00</div>
                                </el-col>
                                <el-col :span="2">
                                    <el-divider direction="vertical"></el-divider>
                                </el-col>
                                <el-col :span="11" style="transform: translate(40%, 0%);">
                                    <div style="font-size:12px;color: rgb(157, 162, 167);">开始</div>
                                    <div style="font-size:18px;font-weight:600;color: rgb(5, 13, 20);">周二</div>
                                    <div style="font-size:18px;font-weight:600;color: rgb(5, 13, 20);">05月10日</div>
                                    <div style="font-size:12px;color: rgb(157, 162, 167);">08:00</div>
                                </el-col>
                            </el-card>
                            <span>排版名称:test1</span>
                            <el-divider></el-divider>
                            <div style="transform: translate(40%, 0%);margin-bottom:10px">下次值班信息</div>
                            <el-card>
                                <el-col :span="11" style="transform: translate(40%, 0%);">
                                    <div style="font-size:12px;color: rgb(157, 162, 167);">开始</div>
                                    <div style="font-size:18px;font-weight:600;color: rgb(5, 13, 20);">周二</div>
                                    <div style="font-size:18px;font-weight:600;color: rgb(5, 13, 20);">05月10日</div>
                                    <div style="font-size:12px;color: rgb(157, 162, 167);">08:00</div>
                                </el-col>
                                <el-col :span="2">
                                    <el-divider direction="vertical"></el-divider>
                                </el-col>
                                <el-col :span="11" style="transform: translate(40%, 0%);">
                                    <div style="font-size:12px;color: rgb(157, 162, 167);">开始</div>
                                    <div style="font-size:18px;font-weight:600;color: rgb(5, 13, 20);">周二</div>
                                    <div style="font-size:18px;font-weight:600;color: rgb(5, 13, 20);">05月10日</div>
                                    <div style="font-size:12px;color: rgb(157, 162, 167);">08:00</div>
                                </el-col>
                            </el-card>
                        </el-card>
                    </el-col>
                </el-row>
            </el-tab-pane>
            <el-tab-pane label="告警详情">
                <alarm-detail></alarm-detail>
            </el-tab-pane>
            <el-tab-pane label="分析">
                <analyse />
            </el-tab-pane>
            <el-tab-pane label="定时任务补偿">
                <test-content/>
            </el-tab-pane>
        </el-tabs>

    </div>
</template>
<script>
import StatisticAnalysis from '@/views/component/pageFour/StatisticAnalysis'
import AlarmDetail from '@/views/component/pageFour/AlarmDetail'
import testContent from '@/views/component/pageFour/testContent'
import Analyse from "@/views/component/pageFour/Analyse"
export default {
    components: {
        StatisticAnalysis,
        AlarmDetail,
        testContent,
        Analyse
    },
    data() {
        return {
            list: [
                [
                    {
                        name: '待处理',
                        key: '1',
                        time: '05-10 15:18',
                        tag: 'test2',
                        severity: 'warning',
                        description: '21527326-7a6a-234334j8vf-9',
                        value: 'localhost of /pod/784-34534-45345-34534534-23-g-5'
                    }, {
                        name: '待处理',
                        key: '2',
                        time: '05-10 15:18',
                        tag: 'test2',
                        severity: 'danger',
                        description: '21527326-7a6a-234334j8vf-40s',
                        value: 'localhost of /pod/784-34534-45345-34534534-23-g-5'
                    }, {
                        name: '待处理',
                        key: '8',
                        time: '05-10 15:18',
                        tag: 'test2',
                        severity: 'danger',
                        description: '21527326-7a6a-234334j8vf-40s',
                        value: 'localhost of /pod/784-34534-45345-34534534-23-g-5'
                    },
                ],
                [
                    {
                        name: '处理中',
                        key: '3',
                        time: '05-10 15:18',
                        tag: 'test2',
                        severity: 'normal',
                        description: '21527326-7a6a-234334j8vf-40s-4ic9340',
                        value: 'localhost of /pod/784-34534-45345-345345-45t-g-5'
                    },
                    {
                        name: '处理中',
                        key: '5',
                        time: '05-10 15:18',
                        tag: 'test2',
                        severity: 'warning',
                        description: '21527326-7a6a-234334j8vf-40s-4ic9340',
                        value: 'localhost of /pod/784-34534-45345-345345-45t-g-5'
                    },
                    {
                        name: '处理中',
                        key: '6',
                        time: '05-10 15:18',
                        tag: 'test2',
                        severity: 'normal',
                        description: '21527326-7a6a-234334j8vf-40s-4ic9340',
                        value: 'localhost of /pod/784-34534-45345-345345-45t-g-5'
                    },
                ],
                [
                    {
                        name: '已关闭',
                        key: '4',
                        time: '05-10 15:18',
                        tag: 'test2',
                        severity: 'danger',
                        description: '21527326-7a6a-234334j8vf-40s-4ic9340',
                        value: 'localhost of /pod/784-34534-45345-345-g45f-45t-g-5'
                    },
                    {
                        name: '处理中',
                        key: '7',
                        time: '05-10 15:18',
                        tag: 'test2',
                        severity: 'normal',
                        description: '21527326-7a6a-234334j8vf-40s-4ic9340',
                        value: 'localhost of /pod/784-34534-45345-345345-45t-g-5'
                    },
                    {
                        name: '处理中',
                        key: '9',
                        time: '05-10 15:18',
                        tag: 'test2',
                        severity: 'normal',
                        description: '21527326-7a6a-234334j8vf-40s-4ic9340',
                        value: 'localhost of /pod/784-34534-45345-345345-45t-g-5'
                    },
                ]
            ],
            descriptionList: [
                {
                    title: '01 xuhaoxiang'
                },
                {
                    title: '02 xuhaoxiang'
                },
            ],
            radio1: '发生时'
        }
    },
    methods: {
        getColor(val) {
            switch (val) {
                case 'normal':
                    return '#3e7fe9'
                case 'danger':
                    return '#e55c6b';
                case 'warning':
                    return '#f4c15e'
            }
        },
    }
}
</script>
<style scoped>
.boss {
    height: 60px;
    width: 210px;
    position: relative;
    background-color: #f5dde0;
    border-radius: 20px 20px 20px 20px;
    box-shadow: 4px 4px 5px #fce6e7;
}

.father {
    height: 50px;
    width: 200px;
    /* border: 1px solid pink; */
    border-radius: 20px 20px 20px 20px;
    background-color: #fff;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.son {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.row {
    margin-bottom: 20px;
    display: flex;
    flex-wrap: wrap;
}

.card {
    min-width: 100%;
    height: 100%;
    margin-right: 20px;
    transition: all .5s;
}

::v-deep .el-divider--vertical {
    display: inline-block;
    width: 1px;
    height: 200px;
    margin: 0 8px;
    vertical-align: middle;
    position: relative;
}
</style>