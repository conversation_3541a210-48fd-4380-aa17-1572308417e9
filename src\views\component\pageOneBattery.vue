<!--
 * @Author: tianrui.li <EMAIL>
 * @Date: 2023-06-05 17:46:50
 * @LastEditors: tianrui.li <EMAIL>
 * @LastEditTime: 2023-06-05 18:08:11
 * @FilePath: \vueDemo\src\views\component\PageOneChart.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div>
        <charts :option="option" style="height:200px; width:100%"></charts>
    </div>
</template>
<script>
import charts from '../../components/charts/Echarts.vue'
export default {
  components: {
    charts
  },
  data () {
    return {
      option: {
        tooltip: {
          trigger: 'item'
        },
        series: [
          {
            name: 'Access From',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 40,
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: [
              { value: 1048, name: 'Search Engine' },
              { value: 735, name: 'Direct' },
              { value: 580, name: 'Email' },
              { value: 484, name: 'Union Ads' },
              { value: 300, name: 'Video Ads' }
            ]
          }
        ]
      }
    }
  }
}
</script>
