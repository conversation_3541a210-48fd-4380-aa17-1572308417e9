<!--
 * @Author: tianrui.li <EMAIL>
 * @Date: 2023-03-03 11:11:11
 * @LastEditors: tianrui.li <EMAIL>
 * @LastEditTime: 2023-05-09 17:21:25
 * @FilePath: \vueDemo\src\components\CommonHeader.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div class="header-container">
        <div class="l-content">
          <el-button style="margin-right:20px" icon="el-icon-menu" size="mini" @click="handleMenu"></el-button>
          <!-- 面包屑 -->
          <el-breadcrumb separator="/">
            <el-breadcrumb-item v-for="item in tags" :key="item.path" :to="{ path: item.path }">{{item.label}}</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        <div class="r-content">
          <el-dropdown @command="handleClick">
            <span class="el-dropdown-link">
              <img class="user" src="../assets/images/user.png" alt="">
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item>个人中心</el-dropdown-item>
              <el-dropdown-item command="cancel">退出</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
    </div>
</template>
<script>
import { mapState } from 'vuex'
import Cookie from 'js-cookie'
export default{
  data () {
    return {

    }
  },
  methods: {
    handleMenu () {
      this.$store.commit('collapseMenu')
    },
    handleClick (command) {
      if (command === 'cancel') {
        // 清清除Cookie得token信息
        Cookie.remove('token')
        // 跳转到登录页面
        this.$router.push('/login')
      }
    }
  },
  computed: {
    // 调用这个方法会返回一个对象，所以使用扩展运算符
    ...mapState({
      tags: state => state.tab.tabsList
    })
  },
  // 验证是否取到了tab.js里面的数据
  mounted () {
  }
}
</script>
<style lang="less" scoped>
.header-container {
  padding: 0 20px;
  background-color: #333;
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .text{
    color:#fff;
    font-size: 14px;
    margin-left: 10px;
  }
  .r-content{
    .user{
      width:40px;
      height:40px;
      border-radius:50%
    }
  }
  .l-content{
    display: flex;
    align-items: center;
    /deep/ .el-breadcrumb__item{
      .el-breadcrumb__inner{
        &.is-link{
          color:#666
        }
      }
      &:last-child{
        .el-breadcrumb__inner {
          color: #fff
        }
      }
    }
  }
}
// .header-container{
//   padding: 0 20px;
//     background-color: #333;
//     height: 60px;
//     display: flex;
//     justify-content: space-between;
//     align-items: center;
// }
// .header-container .text{
//   color: #fff;
//   font-size: 14px;
//   margin-left: 10px;
// }
// .header-container .r-content .user{
//   width: 40px;
//   height: 40px;
//   border-radius:50%;
// }
// .l-content {
//   display: flex;
//   align-items: center;
// }
</style>
