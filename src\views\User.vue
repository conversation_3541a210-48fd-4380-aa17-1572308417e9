<!--
 * @Author: tianrui.li <EMAIL>
 * @Date: 2023-02-21 15:54:14
 * @LastEditors: tianrui.li <EMAIL>
 * @LastEditTime: 2023-06-27 16:00:51
 * @FilePath: \vueDemo\src\views\User.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div class="manage">
      <el-dialog :title="title" :visible.sync="dialogVisible" width="40%" :before-close="handleClose">
        <!-- 用户的表单信息 -->
        <el-form :model="form" ref="form" label-width="100px" :inline="true" :rules="rules">
          <el-form-item label="姓名:" prop="name">
            <el-input size="mini" v-model="form.name" placeholder="请输入姓名"></el-input>
          </el-form-item>
          <el-form-item label="年龄:" prop="age">
            <el-input size="mini" v-model="form.age" placeholder="请输入年龄"></el-input>
          </el-form-item>
          <el-form-item label="性别:" prop="sex">
            <el-select size="mini" v-model="form.sex" placeholder="请选择性别">
              <el-option label="男" :value="1"></el-option>
              <el-option label="女" :value="0"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="出生日期:" prop="birth">
            <el-date-picker size="mini"
              v-model="form.birth"
              type="date"
              placeholder="选择日期"
              value-format="yyyy-MM-DD"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="地址" prop="addr">
            <el-input size="mini" v-model="form.addr" placeholder="请输入地址"></el-input>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="cancel">取消</el-button>
          <el-button type="primary" @click="submit">确定</el-button>
        </span>
      </el-dialog>
      <div class="manage-header">
        <el-button type="primary" size="mini" @click="handleAdd">+ 新增</el-button>
        <el-form :model="userForm" :inline="true" size="mini" style="margin-top:10px" @submit.native.prevent>
          <el-form-item>
            <el-input placeholder="请输入姓名" v-model="userForm.name" clearable @keyup.enter.native="handleSearch"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary"  @click="handleSearch">查询</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="common-table">
        <el-table
          :data="tableData"
          style="width: 100%">
          <el-table-column
            label="缩略图"
          >
            <template slot-scope="scope">
              <el-image style="width: 20px;height:20px" :src="url" :fit="fit"></el-image>
            </template>
          </el-table-column>
          <el-table-column
            prop="name"
            label="姓名">
          </el-table-column>
          <el-table-column
            prop="sex"
            label="性别">
            <template slot-scope="scope">
              <span>{{ scope.row.sex == '0' ? '女' : '男' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="age"
            label="年龄">
          </el-table-column>
          <el-table-column
            prop="birth"
            label="出生日期">
          </el-table-column>
          <el-table-column
            prop="addr"
            label="地址">
          </el-table-column>
          <el-table-column
            label="操作">
            <template slot-scope="scope">
              <el-button size="mini" @click="handleEdit(scope.row)">编辑</el-button>
              <el-button type="danger" size="mini" @click="handleDelete(scope.row)">删除</el-button>
              <el-button type="primary" size="mini" @click="openDrawer(scope.row)">打开抽屉</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pager">
          <el-pagination
            layout="prev,pager,next"
            :total="total"
            @current-change="handlePage"
        ></el-pagination>
        </div>
      </div>
      <drawer :opendrawer="opendrawer"/>
    </div>
</template>
<script>
import { getUser, addUser, editUser, delUser } from '../api'
import Drawer from './Drawer.vue'
export default{
  components: {
    Drawer
  },
  data () {
    return {
      fit: 'fill',
      url: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
      dialogVisible: false,
      form: {
        name: '',
        sex: '',
        age: '',
        birth: '',
        addr: ''
      },
      rules: {
        name: [
          {required: true, message: '请输入姓名', trigger: 'blur'}
        ],
        age: [
          {required: true, message: '请输入年龄', trigger: 'blur'}
        ],
        sex: [
          {required: true, message: '请选择性别', trigger: 'blur'}
        ],
        birth: [
          {required: true, message: '请选择出生日期', trigger: 'blur'}
        ],
        addr: [
          {required: true, message: '请输入地址', trigger: 'blur'}
        ]
      },
      tableData: [],
      title: '',
      modalType: 0, // 0 表示新增弹窗，1表示编辑弹窗
      total: 0,
      pageData: {
        page: 1,
        limit: 10
      },
      userForm: {
        name: ''
      },
      opendrawer: {
        open: false,
        name: '',
        sex: '',
        age: '',
        birth: '',
        addr: ''
      }
    }
  },
  mounted () {
    this.getList()
  },
  methods: {
    // 弹窗关闭的时候会触发
    handleClose () {
      this.$refs.form.resetFields()
      this.dialogVisible = false
    },
    cancel () {
      this.handleClose()
    },
    // 提交用户表单
    submit () {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.modalType === 0) {
            addUser(this.form).then(() => {
              // 重新获取列表的接口
              this.getList()
            })
          } else {
            editUser(this.form).then(() => {
              this.getList()
            })
          }
          //  清空表单数据
          this.$refs.form.resetFields()
          // 关闭弹窗
          this.dialogVisible = false
        }
      })
    },
    handleEdit (row) {
      this.modalType = 1
      this.dialogVisible = true
      // 这个值不能直接复制给form this.form = row，后面对form进行操作的时候会修改form
      // 需要对当前行数据进行深拷贝，否则会出错
      this.form = JSON.parse(JSON.stringify(row))
    },
    handleDelete (row) {
      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true
      }).then(() => {
        delUser({id: row.id}).then(() => {
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.getList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    handleAdd () {
      this.modalType = 0
      this.dialogVisible = true
    },
    // 获取列表的数据
    getList () {
      var str="1,2,3";
      str=str.split(',').join('|')//'1*2*3'
      getUser({params: {...this.userForm, ...this.pageData}}).then(({data}) => {
        this.tableData = data.list
        this.total = data.count || 0
      })
    },
    // 选择页码的回调
    handlePage (val) {
      this.pageData.page = val
      this.getList()
    },
    // 列表搜索条件
    handleSearch () {
      this.getList()
    },
    // 打开抽屉
    openDrawer (row) {
      this.opendrawer.open = true
      this.opendrawer.name = row.name
      this.opendrawer.sex = row.sex
      this.opendrawer.birth = row.birth
      this.opendrawer.addr = row.addr
    }
  }
}
</script>
<style lang="less" scoped>
.manage {
  height: 90%;
  .manage-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .common-table {
    position: relative;
    height: calc(100%- 62px);
    .pager {
      position: absolute;
      bottom: 0;
      top: 600px;
      right: 20px
    }
  }
}
</style>
