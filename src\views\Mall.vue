<!--
 * @Author: tianrui.li <EMAIL>
 * @Date: 2023-02-22 09:56:18
 * @LastEditors: tianrui.li <EMAIL>
 * @LastEditTime: 2023-08-11 11:30:49
 * @FilePath: \vueDemo\src\views\Mall.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <el-card>
      <el-row :gutter="15">
        <el-col :span="6" v-for="item in cardList" :key="item.id">
          <el-card class="card" :style="{ backgroundColor: item.color }">
            <div class="top"><span class="numberTotal">{{ item.total }}</span><span class="numberUsed">/{{ item.used
            }}</span></div>
            <div class="description">{{ item.description }}</div>
          </el-card>
        </el-col>
      </el-row>
    </el-card>
    <br>
    <el-card>
      <el-tabs v-model="activeName" :before-leave="beforeLeave">
        <!-- <el-button type="primary" plain>刷新</el-button> -->
        <el-tab-pane name="alert" :lazy="true">
          <span slot="label">告警收敛与根因显示</span>
          <el-table :data="alertTableData">
            <el-table-column label="算法ID"></el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane name="history" :lazy="true">
          <span slot="label">历史告警</span>
          <el-table :data="historyTableData">
            <el-table-column label="算法ID"></el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane name="CustoBtn">
          <span slot="label">
            <template v-if="!refresh">
              <el-button type="primary" size="mini" plain
                @click="handleRefresh">刷新</el-button>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            </template>
            <template v-else>
              <el-button type="primary" size="mini" plain
                @click="handleSuspend">暂停</el-button>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            </template>
            <span style="font-size:10px;color:gray" v-if="refresh"><span id="countdown"></span>秒后刷新</span>
          </span>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script>
import { groupBy } from "lodash"
export default {
  data() {
    return {
      input1: '',
      readOnlyPart: '这是只读部分的文本',
      showType: 'down',
      cardList: [
        { id: '1', total: '123', used: '12', description: '1小时内推荐根因/聚类个数', color: '#6b9ef8' },
        { id: '2', total: '150', used: '12', description: '1小时内推荐根因/聚类个数', color: '#5ea884' },
        { id: '3', total: '0', used: '0', description: '1小时内推荐根因/聚类个数', color: '#6b9ef8' },
        { id: '4', total: '18724', used: '360', description: '1小时内告警/聚类个数', color: '#6b9ef8' }
      ],
      activeName: 'alert',
      alertTableData: [],
      historyTableData: [],
      refresh: false,
      countdown: 6
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      // 定义函数
      const isContained = (a, b) => {
        // a和b其中一个不是数组，直接返回false
        if (!(a instanceof Array) || !(b instanceof Array)) return false;
        const len = b.length;
        // a的长度小于b的长度，直接返回false
        if (a.length < len) return false;
        for (let i = 0; i < len; i++) {
          // 遍历b中的元素，遇到a没有包含某个元素的，直接返回false
          if (!a.includes(b[i])) return false;
        }
        // 遍历结束，返回true
        return true;
      };

      let arr = [['Application:Battery', 'Application:Port'], 'Component:Mtrees']
      let a1 = ['Application:Port', 'Application:Battery']
      for (let i = 0; i < arr.length; i++) {
      }
    },
      handle(item) {
        const data = ['Powerstore', 'powerstore', 'Unity', 'unity', 'isilon', 'Isilon', 'dd', 'DD', 'poweredge', 'powerEdge', 'PowerEdge', 'vnx', 'Vnx', 'VNX', '3par', '3Par', 'primera', 'Primera', 'vplex', 'vPlex', 'Vplex']
      },
      changeRadio(val) {
      },
      beforeLeave(visitName, currentName) {
        if (visitName === 'CustoBtn') {
          return false
        }
      },
      // 刷新
      handleRefresh() {
        this.refresh = true
        if (this.refresh) {
          let countdownInterval = setInterval(function () {
            this.countdown--
            document.getElementById('countdown').innerHTML = this.countdown
          }, 1000)
          setTimeout(function () {
            clearInterval(countdownInterval)
          }, 6000)
        }
      },
      handleSuspend() {
        this.refresh = false
      }
    }
  }
</script>
<style scoped>
.card {
  height: 150px;
  border-radius: 25px;
}

.top {
  margin-top: 20px;
  margin-left: 15px;
}

.numberUsed {
  color: white;
  font-size: 15px;
}

.numberTotal {
  color: white;
  font-size: 30px
}

.description {
  margin-left: 15px;
  color: white;
  font-size: 18px
}
</style>
