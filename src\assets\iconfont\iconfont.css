@font-face {
  font-family: "iconfont"; /* Project id 3302248 */
  src: url('iconfont.woff2?t=1687940571304') format('woff2'),
       url('iconfont.woff?t=1687940571304') format('woff'),
       url('iconfont.ttf?t=1687940571304') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-mail:before {
  content: "\e649";
}

.icon-phone:before {
  content: "\e6a0";
}

.icon-wangluojiekou:before {
  content: "\e631";
}

.icon-ceshi:before {
  content: "\e658";
}

.icon-minsu_:before {
  content: "\e644";
}

