/*
 * @Author: tianrui.li <EMAIL>
 * @Date: 2023-03-06 15:46:38
 * @LastEditors: tianrui.li <EMAIL>
 * @LastEditTime: 2023-05-04 10:59:09
 * @FilePath: \vueDemo\src\utils\request.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import axios from 'axios'

const http = axios.create({
  //  通用请求的地址前缀
  baseURL: '/api',
  timeout: 10000 // 超时时间
})
export default http

//  添加请求拦截器
axios.interceptors.request.use(function (config) {
  // 在发送之前做些什么
  return config
}, function (error) {
  // 对请求错误做些什么
  return Promise.reject(error)
})

// 添加响应拦截器
http.interceptors.response.use(function (response) {
  // 对响应数据做点什么
  return response
}, function (error) {
  // 对响应错误做点什么
  return Promise.reject(error)
})
// 图片转为base64格式
export const imageToBase64 = (url, ext, callback) => {
  let canvas = document.createElement('canvas') // 创建canvas DOM元素
  const ctx = canvas.getContext('2d')
  // eslint-disable-next-line no-var
  const img = new Image()
  img.crossOrigin = 'Anonymous'
  img.src = url
  img.onload = function () {
    canvas.width = img.width
    canvas.height = img.height
    ctx.drawImage(img, 0, 0, img.height, img.width) // 参数可自定义
    // eslint-disable-next-line no-shadow
    const dataURL = canvas.toDataURL(`image/${ext}`).split(':')[1]
    // const dataURL = canvas.toDataURL(`image/${ext}`).split(':')[1]
    console.log(dataURL)
    return dataURL
    // callback(dataURL) // 回掉函数获取Base64编码
    // canvas = null
  }
}
