<!--
 * @Author: tianrui.li <EMAIL>
 * @Date: 2023-05-08 17:54:18
 * @LastEditors: tianrui.li <EMAIL>
 * @LastEditTime: 2023-05-09 14:31:21
 * @FilePath: \vueDemo\src\views\Child.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-drawer
    title="title"
    :visible.sync="drawerData.visible"
    size="70%"
    direction="rtl"
    >
    <el-table :data="tableData">
      <el-table-column label="ID" prop="id"></el-table-column>
      <el-table-column label="name" prop="name"></el-table-column>
      <el-table-column label="value" prop="value"></el-table-column>
    </el-table>
  </el-drawer>
</template>
<script>
export default {
  props: {
    drawerData: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      tableData: []
    }
  },
  watch: {
    'drawerData.name': {
      handler (value) {
        this.getList(value)
      }
    }
  },
  created () {
    this.getList(this.drawerData.id)
  },
  methods: {
    getList (id) {
      console.log(id)
    },
    getData (data) {
      console.log(data)
      console.log('id变了')
    }
  }
}
</script>
