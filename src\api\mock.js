/*
 * @Author: tianrui.li <EMAIL>
 * @Date: 2023-03-06 16:12:08
 * @LastEditors: tianrui.li <EMAIL>
 * @LastEditTime: 2023-05-09 16:58:41
 * @FilePath: \vueDemo\src\api\mock.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import Mock from 'mockjs'
import homeApi from './mockServerData/home'
import user from './mockServerData/user'
import permission from './mockServerData/permission'
// 定义mock请求拦截
Mock.mock('/api/home/<USER>', homeApi.getStatisticalData)
// 用户列表的数据
Mock.mock('/api/user/add', 'post', user.createUser)
Mock.mock('/api/user/edit', 'post', user.updateUser)
Mock.mock('/api/user/del', 'post', user.deleteUser)
Mock.mock(/api\/user\/getUser/, user.getUserList)

Mock.mock(/api\/permission\/getMenu/, 'post', permission.getMenu)
