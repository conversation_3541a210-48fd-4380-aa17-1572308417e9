/*
 * @Author: tianrui.li <EMAIL>
 * @Date: 2023-06-05 16:34:13
 * @LastEditors: tianrui.li <EMAIL>
 * @LastEditTime: 2023-06-05 16:58:45
 * @FilePath: \vueDemo\src\assets\icons\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import Vue from 'vue'
import SvgIcon from '../../components/SvgIcon'
Vue.component('svg-icon', SvgIcon) // 全局注册SvgIcon
const req = require.context('../../assets/icons/svg', false, /\.svg$/)
const requireAll = requireContext => {
  // requireContext.keys()数据：['./404.svg', './agency.svg', './det.svg', './user.svg']
  requireContext.keys().map(requireContext)
}
requireAll(req)
