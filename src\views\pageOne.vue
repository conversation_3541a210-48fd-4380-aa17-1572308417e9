<!--
 * @Author: tianrui.li <EMAIL>
 * @Date: 2023-02-22 09:57:59
 * @LastEditors: tianrui.li <EMAIL>
 * @LastEditTime: 2023-07-09 19:24:09
 * @FilePath: \vueDemo\src\views\pageOne.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <el-tabs v-model="activeTab">
      <el-tab-pane label="列表展示" name="first">
        <el-input v-model="keyword"></el-input>
        <el-table :data="filteredTableData" style="width: 100%">
          <el-table-column prop="date" label="日期" width="180">
          </el-table-column>
          <el-table-column prop="name" label="姓名" width="180">
          </el-table-column>
          <el-table-column prop="address" label="地址">
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="图文展示" name="second">
        <span slot="label">图文展示</span>
        <el-row :gutter="10" class="row">
          <el-col :span="6">
            <el-tree :data="data" :props="defaultProps" @node-click="handleNodeClick"></el-tree>
          </el-col>
          <el-col :span="18">
            <el-card v-for="i in 4" :key="i" style="margin-top:5px" class="card">
              <el-link type="warning">[操作系统]</el-link>
              <el-link type="primary">Windows主机带宽和CPU跑满或跑高排查-正常跑满或跑高的分析处理</el-link>
              <div style="margin-top: 10px;font-size:14px">
                <span>作者: {{ author }}</span>
                <span>最新回复时间：</span>
              </div>
              <div style="margin-top:10px;font-size:14px">
                <span>简介: Windows主机带宽和CPU跑满或跑高排查-正常跑满或跑高的分析处理</span>
              </div>
              <div style="margin-top: 10px;font-size:14px">
                <span style="margin-right: 8px"><svg-icon icon-file-name="bang" />23</span>
                <span style="margin-right: 8px">1</span>
                <span>发表时间: 2023-04-17 14:20:51</span>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
// import PageOneBattery from './component/PageOneBattery'
import PageOneChart from './component/PageOneChart'
export default {
  components: {
    PageOneChart
    // PageOneBattery
  },
  data() {
    return {
      author: 'Admin',
      length: '12',
      value: '',
      tableData: [
        {
          severity: '2',
          description: '[虚拟机]剩下不到1%的可用空间/data 在 ************.',
          host: '************',
          date: '2023-04-18 16:32:13'
        }
      ],
      activeTab: 'first',
      data: [{
        label: '全部',
        children: [
          {
            label: '操作系统'
          },
          {
            label: '中间件'
          },
          {
            label: '网络设备'
          },
          {
            label: '应用'
          }
        ]
      }],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      keyword: '',
      listData: [{
            date: '2016-05-02',
            name: '王小1',
            address: '上海市普陀区金沙江路 1518 弄'
          }, {
            date: '2016-05-04',
            name: '王小2',
            address: '上海市普陀区金沙江路 1517 弄'
          }, {
            date: '2016-05-01',
            name: '王小san',
            address: '上海市普陀区金沙江路 1519 弄'
          }, {
            date: '2016-05-03',
            name: '王小4',
            address: '上海市普陀区金沙江路 1516 弄'
          }],
    }
  },
  computed:{
    filteredTableData(){
      if(this.keyword){
        return this.listData.filter((item) => {
          return (
            item.name.includes(this.keyword) ||
            item.date.includes(this.keyword) ||
            item.address.includes(this.keyword)
          )
        })
      }else{
        return this.listData
      }
    }
  },
  created() {
  },
  methods: {
    // 树形
    handleNodeClick(data) {
    },
    cellClassName({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 1) {
        switch (row.severity) {
          case '0':
            return 'notClassified-row'
          case '1':
            return 'information-row'
          case '2':
            return 'warning-row'
          case '3':
            return 'average-row'
          case '4':
            return 'high-row'
          default:
            return 'disaster-row'
        }
      }
    }
  }
}
</script>
<style scoped>
::v-deep .el-row {
  margin-bottom: 20px;
  display: flex;
  flex-wrap: wrap;
}

::v-deep .el-row .el-card {
  min-width: 100%;
  height: 100%;
  margin-right: 20px;
  transition: all .5s;
}
</style>
