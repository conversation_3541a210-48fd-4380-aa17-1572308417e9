<!--
 * @Author: tianrui.li <EMAIL>
 * @Date: 2023-04-20 16:39:17
 * @LastEditors: tianrui.li <EMAIL>
 * @LastEditTime: 2023-04-20 17:18:46
 * @FilePath: \vueDemo\src\components\commonTag.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div class="tabs">
        <el-tag
            v-for="(item,index) in tags"
            size="mini"
            :key="item.path"
            :closable="item.name !== 'home'"
            :effect="$route.name === item.name ? 'dark' : 'plain'"
            @click="changeMenu(item)"
            @close="handleClose(item,index)"
            >
            {{ item.label }}
        </el-tag>
    </div>
</template>
<script>
import {mapState, mapMutations} from 'vuex'
export default{
  name: 'commonTab',
  data () {
    return {
    }
  },
  computed: {
    ...mapState({
      tags: state => state.tab.tabsList
    })
  },
  methods: {
    // 通过这种方法把store里的mutation添加到当前页面中
    ...mapMutations(['closeTag']),
    // 点击tag跳转的功能
    changeMenu (item) {
      this.$router.push({name: item.name})
    },
    // tag标签删除的逻辑
    handleClose (item, index) {
    // 调用store中的mutation
      this.closeTag(item)
      const length = this.tags.length
      // 删除之后的跳转逻辑
      if (item.name !== this.$route.name) {
        return
      }
      // 表示删除的是最后一项
      if (index === length) {
        this.$router.push({
          name: this.tags[index - 1].name
        })
        // // 删除的是中间位置
      } else {
        this.$router.push({
          name: this.tags[index].name
        })
      }
    }
  }
}
</script>
<style lang="less" scoped>
    .tabs{
        padding: 20px;
        .el-tag{
            margin-right: 15px;
            cursor: pointer
        }
    }
</style>
