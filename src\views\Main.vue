<!--
 * @Author: tianrui.li <EMAIL>
 * @Date: 2023-02-21 16:12:03
 * @LastEditors: tianrui.li <EMAIL>
 * @LastEditTime: 2023-04-20 16:47:38
 * @FilePath: \vueDemo\src\views\Main.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div>
        <el-container>
            <el-aside width="auto">
                <common-aside></common-aside>
            </el-aside>
            <el-container>
            <el-header>
              <common-header/>
            </el-header>
            <common-tag/>
            <el-main>
                <!-- 路由出口 -->
                <!-- 路由匹配到的组件将渲染在这里 -->
                <router-view></router-view>
            </el-main>
            </el-container>
        </el-container>
    </div>
</template>
<script>
import commonAside from '../components/commonAside.vue'
import commonHeader from '../components/commonHeader.vue'
import commonTag from '../components/commonTag.vue'
export default{
  components: {
    commonAside,
    commonHeader,
    commonTag
  },
  data () {
    return {

    }
  }
}
</script>
<style scoped>
.el-header{
  padding:0
}
</style>
