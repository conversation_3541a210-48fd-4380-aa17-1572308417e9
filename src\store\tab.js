/*
 * @Author: tianrui.li <EMAIL>
 * @Date: 2023-03-03 11:46:54
 * @LastEditors: tianrui.li <EMAIL>
 * @LastEditTime: 2023-05-11 10:04:57
 * @FilePath: \vueDemo\src\store\tab.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import Cookie from 'js-cookie'
export default {
  // 监控的数据
  state: {
    isCollapse: false, // 用于控制菜单展开还是收起
    // 面包屑的数据
    tabsList: [
      {
        path: '/',
        name: 'home',
        label: '首页',
        icon: 's-home',
        url: 'Home/Home'
      }
    ],
    menu: []
  },
  mutations: {
    // 修改菜单展开收起的方法
    collapseMenu (state) {
      state.isCollapse = !state.isCollapse
    },
    // 更新面包屑数据
    selectMenu (state, val) {
      // 判断添加的数据是否为首页
      if (val.name !== 'home') {
        const index = state.tabsList.findIndex(item => item.name === val.name)
        // 如果不存在
        if (index === -1) {
          state.tabsList.push(val)
        }
      }
    },
    // 用来删除指定的tag数据
    closeTag (state, item) {
      const index = state.tabsList.findIndex(val => val.name === item.name)
      state.tabsList.splice(index, 1)
    },
    // 设置menu的数据
    setMenu (state, val) {
      state.menu = val
      Cookie.set('menu', JSON.stringify(val))
    },
    // 动态注册路由
    addMenu (state, router) {
      // 判断缓存中是否有数据
      if (!Cookie.get('menu')) return
      const menu = JSON.parse(Cookie.get('menu'))
      state.menu = menu
      // 组装动态路由的数据
      const menuArray = []
      menu.forEach(item => {
        if (item.children) {
          item.children = item.children.map(item => {
            item.component = () => import(`../views/${item.path}`)
            return item
          })
          menuArray.push(...item.children)
        } else {
          item.component = () => import(`../views/${item.path}`)
        }
      })
    }
  }
}
