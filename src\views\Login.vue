<!--
 * @Author: tianrui.li <EMAIL>
 * @Date: 2023-05-09 15:48:00
 * @LastEditors: tianrui.li <EMAIL>
 * @LastEditTime: 2023-05-11 10:05:05
 * @FilePath: \vueDemo\src\views\Login.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <el-form ref="form" label-width="70px" class="login-container" :model="form" :inline="true" :rules="rules">
        <h3 class="login_title">系统登录</h3>
        <el-form-item label="用户名" prop="username">
            <el-input v-model="form.username" placeholder="请输入账号"></el-input>
        </el-form-item>
        <el-form-item label="密码" prop="password">
            <el-input type="password" v-model="form.password" placeholder="请输入密码"></el-input>
        </el-form-item>
        <el-form-item>
            <el-button style="margin-top:10px" type="primary" @click="submit">登录</el-button>
        </el-form-item>
    </el-form>
</template>
<script>
import Cookie from 'js-cookie'
import { getMenu } from '../api'
export default {
  data () {
    return {
      form: {
        username: '',
        password: ''
      },
      rules: {
        username: [{required: true, trigger: 'blur', message: '请输入用户名'}],
        password: [{required: true, trigger: 'blur', message: '请输入密码'}]
      }
    }
  },
  methods: {
    //  登录
    submit () {
    // token信息
    //   const token = Mock.Random.guid()
    //   // token信息存入cookie用于不同页面间得通信
    //   Cookie.set('token', token)
    // 校验通过
      this.$refs.form.validate((valid) => {
        if (valid) {
          getMenu(this.form).then(({ data }) => {
            if (data.code === 20000) {
              Cookie.set('token', data.data.token)
              // 获取菜单的数据，存入store中
              // data.data.menu
              // 调用 mutitation方法
              this.$store.commit('setMenu', data.data.menu)
              this.$store.commit('addMenu', this.$router)
              //   页面跳转
              this.$router.push('/home')
            } else {
              this.$message.error(data.data.message)
            }
          })
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.login-container {
    text-align: center;
    width: 350px;
    border: 1px solid #eaeaea;
    margin: 180px auto;
    padding: 35px 35px 15px 35px;
    background-color: #fff;
    border-radius: 15px;
    box-shadow: 0 0 25px #cac6c6;
    box-sizing: border-box;
    .el-input{
        width: 198px;
    }
    .login_title{
        text-align: center;
        margin-bottom: 40px;
        color: #505458;
    }
}
</style>
