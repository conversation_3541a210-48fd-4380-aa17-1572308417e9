<!--
 * @Author: tianrui.li <EMAIL>
 * @Date: 2023-06-05 17:44:30
 * @LastEditors: tianrui.li <EMAIL>
 * @LastEditTime: 2023-06-05 18:05:22
 * @FilePath: \vueDemo\src\components\charts\Echarts.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div ref="chartDom"></div>
  </template>

<script>
import * as echarts from 'echarts'
import debounce from 'lodash/debounce'

export default {
  props: {
    option: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    option: {
      handler (val) {
        this.chart.setOption(val)
      },
      deep: true
    }
  },
  created () {
    if (this.chart) {
      this.resize = debounce(this.resize, 300)
    }
  },
  mounted () {
    this.renderChart()
  },
  beforeDestroy () {
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    resize () {
      this.chart.resize()
    },
    renderChart () {
      this.chart = echarts.init(this.$refs.chartDom)
      this.chart.setOption(this.option)
    }
  }
}
</script>
