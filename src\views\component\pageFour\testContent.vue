<!--
 * @Author: tianrui.li <EMAIL>
 * @Date: 2023-06-29 11:13:45
 * @LastEditors: tianrui.li <EMAIL>
 * @LastEditTime: 2023-06-29 16:03:48
 * @FilePath: \vueDemo\src\views\component\pageFour\testContent.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div>
        <el-row :gutter="8">
            <el-col :span="6">
                <div class="avatar mt20">
                    <div class="angle_mark">
                        <span style="position: absolute; display: inline-block; color:#fff; width: 100%; bottom: 0; left: 0; text-align: center;">{{ val }}</span>
                    </div>
                    <el-card :body-style="{ padding: '5px' }">
                        <img :src="require('@/assets/images/exporter/node.png')" class="image" />
                        <div style="padding: 14px">
                            <span>Node Exporter</span>
                            <div class="description">
                                采集节点相关系统信息，如 CPU、内存、硬盘、网络带宽等。
                            </div>
                            <div class="bottom clearfix">
                                <el-tag type="success">已安装0个Exporter</el-tag>
                                <el-button icon="el-icon-plus" size="mini" style="float: right" @click="openDrawer('Node Exporter')">安装</el-button>
                            </div>
                        </div>
                    </el-card>
                </div>
            </el-col>
            <el-col :span="6">
                <div class="avatar mt20">
                    <div class="angle_mark">
                        <span style="position: absolute; display: inline-block; color:#fff; width: 100%; bottom: 0; left: 0; text-align: center;">{{ val }}</span>
                    </div>
                    <el-card :body-style="{ padding: '5px' }">
                        <img :src="require('@/assets/images/exporter/windows.png')" class="image" />
                        <div style="padding: 14px">
                            <span>Windows</span>
                            <div class="description">
                                收集CPU、内存、磁盘、网络、进程等指标，提供全面的Windows系统监控。
                            </div>
                            <div class="bottom clearfix">
                                <el-tag type="success">已安装0个Exporter</el-tag>
                                <el-button icon="el-icon-plus" size="mini" style="float: right"
                                    @click="openDrawer('Windows')">安装</el-button>
                                <!-- <el-button type="text" class="button">操作按钮</el-button> -->
                            </div>
                        </div>
                    </el-card>
                </div>
            </el-col>
            <el-col :span="6">
                <div class="avatar mt20">
                    <div class="angle_mark">
                        <span style="position: absolute; display: inline-block; color:#fff; width: 100%; bottom: 0; left: 0; text-align: center;">{{ val }}</span>
                    </div>
                    <el-card :body-style="{ padding: '5px' }">
                        <img :src="require('@/assets/images/exporter/mysql.png')" class="image" />
                        <div style="padding: 14px">
                            <span>Mysql</span>
                            <div class="description">
                                收集性能模式指标、查询吞吐量、自定义指标。
                            </div>
                            <div class="bottom clearfix">
                                <el-tag type="success">已安装0个Exporter</el-tag>
                                <el-button icon="el-icon-plus" size="mini" style="float: right"
                                    @click="openDrawer('Mysql')">安装</el-button>
                            </div>
                        </div>
                    </el-card>
                </div>
            </el-col>
        </el-row>
        <el-drawer :title="title" size="70%" :visible.sync="drawer" :direction="direction">
            <div style="margin: 20px;">
                <el-steps direction="vertical" :active="2">
                    <el-step title="选择/创建 Prometheus 实例">
                        <div
                            class="demo-drawer__content"
                            slot="description"
                            style="margin-top:10px"
                        >
                            <el-form
                                :model="ruleForm"
                                :rules="rules"
                                ref="ruleForm"
                                label-width="auto"
                                class="demo-ruleForm"
                            >
                                <el-form-item label="选择集群" prop="prometheusCluster">
                                    <el-select
                                        v-model="ruleForm.prometheusCluster"
                                        style="width:100%"
                                        placeholder="请选择或新建集群"
                                    >
                                        <el-option
                                            v-for="item in options"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value"
                                        ></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-form>
                        </div>
                    </el-step>
                    <el-step title="配置信息">
                        <div
                            class="demo-drawer__content"
                            slot="description"
                            style="margin-top:10px"
                        >
                            <el-tabs v-model="activeName">
                                <el-tab-pane label="配置" name="first">
                                    <el-form
                                        :model="itemForm"
                                        ref="itemForm"
                                        :rules="itemRules"
                                        label-width="120px"
                                        label-position="left"
                                        class="demo-ruleForm"
                                    >
                                        <el-form-item label="Exporter名称" prop="exporterName">
                                            <el-input v-model="itemForm.exporterName" size="small"></el-input>
                                        </el-form-item>
                                        <el-form-item label="IP地址" prop="ipAddress">
                                            <el-input v-model="itemForm.ipAddress" size="small"></el-input>
                                        </el-form-item>
                                        <el-form-item label="Metric采集路径" prop="exporterUrl">
                                            <el-input v-model="itemForm.exporterUrl" size="small"></el-input>
                                        </el-form-item>
                                        <el-form-item label="采集频率(秒)" prop="simplingRate">
                                            <el-input v-model="itemForm.simplingRate" size="small"></el-input>
                                        </el-form-item>
                                        <el-form-item label="描述信息" prop="description">
                                            <el-input type="textarea" v-model="itemForm.textarea" size="small"></el-input>
                                        </el-form-item>
                                    </el-form>
                                </el-tab-pane>
                                <el-tab-pane label="指标" name="itemList">
                                    指标
                                </el-tab-pane>
                            </el-tabs>
                        </div>
                    </el-step>
                </el-steps>
                <div class="demo-drawer__footer" style="float:right">
                    <el-button type="primary" size="small" @click="submitForm">确定</el-button>
                    <el-button size="small" @click="cancel">取消</el-button>
                </div>
            </div>
        </el-drawer>
    </div>
</template>
<script>
export default {
    data() {
        return {
            url: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
            val: "i'm 角标",
            title: '',
            drawer:false,
            direction: 'rtl',
            activeName: 'first',
            // 配置
            ruleForm: {},
            rules:{},
            options:[
                {value:'test1',label:'test1'},
                {value:'test2',label:'test2'},
                {value:'test3',label:'test3'},
                {value:'test4',label:'test4'},
            ],
            // 指标
            itemForm: {},
            itemRules:{
                exporterName:[{required:true,message:'不能为空',trigger:'blur'}],
                ipAddress:[{required:true,message:'不能为空',trigger:'blur'}],
                exporterUrl:[{required:true,message:'不能为空',trigger:'blur'}],
                simplingRate:[{required:true,message:'不能为空',trigger:'blur'}],
            }
        }
    },
    methods:{
        // 安装
        openDrawer(val){
            this.reset()
            this.title = `接入${val}`
            this.drawer = true
        },
        // 重置
        reset(){
            this.ruleForm = {
                prometheusCluster: ''
            }
            this.itemForm = {
                exporterName: '',
                ipAddress: '',
                exporterUrl: '',
                simplingRate: '',
                description: ''
            }
        },
        // 确定
        submitForm(){
            console.log(Object.assign(this.ruleForm,this.itemForm))
            this.drawer = false
        },
        // 取消
        cancel(){
            this.reset()
            this.drawer = false
        }
    }
}
</script>

<style scoped>
.description {
    font-size: 13px;
    color: #999;
    margin-top: 10px;
}

.bottom {
    margin-top: 13px;
    line-height: 12px;
}

.image {
    width: 22%;
    display: block;
    margin-top: 8px;
    margin-left: 8px;
}

.clearfix:before,
.clearfix:after {
    display: table;
    content: "";
}

.clearfix:after {
    clear: both;
}
.mb10 {
    margin-bottom: 10px;
}

.mt20 {
    margin-top: 20px;
}

.avatar {
    width: 400px;
    height: 400px;
    overflow: hidden;
    position: relative;
}

.avatar .angle_mark {
    position: absolute;
    top: -50px;
    right: -50px;
    background-color: #d1a881;
    width: 100px;
    height: 100px;
    transform: rotate(45deg);
}
</style>