<!--
 * @Author: tianrui.li <EMAIL>
 * @Date: 2023-06-28 17:02:03
 * @LastEditors: tianrui.li <EMAIL>
 * @LastEditTime: 2023-07-14 10:57:59
 * @FilePath: \vueDemo\src\views\component\pageFour\AlarmDetail.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <el-table :data="tableData">
        <el-table-column label="tags" prop="zl_host_tag">
            <template slot-scope="scope">
                <el-tag v-for="item in scope.row.zl_host_tag" :key="item.index">{{ item.key + item.value }}</el-tag>
            </template>
        </el-table-column>
        <el-table-column label="操作">
            <template slot-scope="scope">
                <el-button @click="handleUpdate(scope.row)">操作</el-button>
            </template>
        </el-table-column>
    </el-table>
    <el-dialog title="title" :visible.sync="open">
        <el-form :model="form" ref="form">
            <el-form-item label="tags" prop="tags">
                <el-button @click="addInput">添加</el-button>
                <div v-for="(item,index) in form.tags" :key="index">
                    <el-row :gutter="10">
                        <el-col :span="8">
                            <el-input v-model="item.key" placeholder="请输入key"></el-input>
                        </el-col>
                        <el-col :span="8">
                            <el-input v-model="item.value" placeholder="请输入value"></el-input>
                        </el-col>
                        <el-col :span="7" style="float: right">
                            <el-button @click="removeInput(index)">删除</el-button>
                        </el-col>
                    </el-row>
                </div>
            </el-form-item>
        </el-form>
        <div slot="footer">
            <el-button @click="submitForm">确认</el-button>
        </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
    data(){
        return{
            open:false,
            form:{
                tags:[]
            },
            input1:'',
            input2:'',
            tableData:[
                {
                    zl_host_tag:[
                        {key:'cpu',value:''}
                    ]
                },
                {
                    zl_host_tag:[]
                }
            ]
        }
    },
    methods:{
        // 修改
        handleUpdate(row){
            this.open = true
            this.form.tags = row.zl_host_tag
        },
        // 表格提交
        submitForm(){
            console.log(this.form)
        },
        // 新增一行
        addInput(){
            this.form.tags.push({key: '',value:''})
        },
        removeInput(index){
            this.form.tags.splice(index,1)
        }
    }
}
</script>

<style>

</style>