/*
 * @Author: tianrui.li <EMAIL>
 * @Date: 2023-02-20 11:49:35
 * @LastEditors: tianrui.li <EMAIL>
 * @LastEditTime: 2023-06-28 16:00:30
 * @FilePath: \vueDemo\src\main.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import Vue from 'vue'
import App from './App'
import router from './router'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import VueCropper from 'vue-cropper'
import store from './store'
import './api/mock'
import Cookie from 'js-cookie'
import VueCodemirror from 'vue-codemirror'
import 'codemirror/lib/codemirror.css'
import './assets/icons/index'
import './assets/iconfont/iconfont'
Vue.use(VueCropper)
Vue.use(ElementUI)

Vue.config.productionTip = false
Vue.use(VueCodemirror)
// 添加全局前置导航守卫
router.beforeEach((to, from, next) => {
  // 判断token存不存在
  const token = Cookie.get('token')
  // token不存在，说明当前用户未登录，应该跳转至登录页
  if (!token && to.name !== 'login') {
    next({ name: 'login' })
  } else if (token && to.name === 'login') {
    next({ name: 'home' })
  } else {
    next()
  }
})
/* eslint-disable no-new */
new Vue({
  el: '#app',
  router,
  store,
  components: { App },
  template: '<App/>'
})
