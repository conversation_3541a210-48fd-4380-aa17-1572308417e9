
/*空字符串的判断*/
export const isNull = (data) => {
  if (
    data === null ||
    data === "null" ||
    data === undefined ||
    data === "undefined" ||
    data === "" ||
    data === []
  )
    return true;
};
export const processPerformanceItem = (item) => {
  const getTagValue = (tag) => item.tags.find(i => i.tag === tag).value || '-';
  const getValueOrDefault = (obj, key, defaultValue = '-') => isNull(obj[key]) ? defaultValue : obj[key];
  const formatValue = (value, defaultValue = '-') => isNull(value) ? defaultValue : `${Number(value).toFixed(2)}`;
  const formatByteValue = (value, defaultValue = '-') => isNull(value) ? defaultValue : changeByteToNormalData(value);
  const formatNormalValue = (value, defaultValue = '-') => isNull(value) ? defaultValue : value;
  const formatBytes = (value, defaultValue = '-') => isNull(value) ? defaultValue : changeBytesToNormalData(value);

  item.app_name = getTagValue('app_name');
  item.ip = isNull(item.interfaces[0]) ? (item.interfaces[0].dns || '-') : item.interfaces[0].ip;
  item.cpuNum = getValueOrDefault(item.items, 'cpuNum', '-');
  item.cpuUsedMinFormat = formatValue(item.items.cpuTrend.value_min);
  item.cpuUsedAvgFormat = formatValue(item.items.cpuTrend.value_avg);
  item.cpuUsedMaxFormat = formatValue(item.items.cpuTrend.value_max);
  item.memoryUsedMinFormat = formatValue(item.items.memoryTrend.value_min);
  item.memoryUsedAvgFormat = formatValue(item.items.memoryTrend.value_avg);
  item.memoryUsedMaxFormat = formatValue(item.items.memoryTrend.value_max);
  item.systemInfo = getValueOrDefault(item.items, 'systemInfo', '-');
  item.memory1 = formatByteValue(item.items.memory);
  item.memory = item.items.memory;
  item.app_owner = getTagValue('app_owner');
  item.bitSendMax1 = formatBytes(item.items.bitsSentTrend.value_max);
  item.bitSendMin1 = formatBytes(item.items.bitsSentTrend.value_min);
  item.bitSendAvg1 = formatBytes(item.items.bitsSentTrend.value_avg);
  item.bitReceiveMax1 = formatBytes(item.items.bitsReceivedTrend.value_max);
  item.bitReceiveMin1 = formatBytes(item.items.bitsReceivedTrend.value_min);
  item.bitReceiveAvg1 = formatBytes(item.items.bitsReceivedTrend.value_avg);
  item.bitSendMax = formatNormalValue(item.items.bitsSentTrend.value_max);
  item.bitSendMin = formatNormalValue(item.items.bitsSentTrend.value_min);
  item.bitSendAvg = formatNormalValue(item.items.bitsSentTrend.value_avg);
  item.bitReceiveMax = formatNormalValue(item.items.bitsReceivedTrend.value_max);
  item.bitReceiveMin = formatNormalValue(item.items.bitsReceivedTrend.value_min);
  item.bitReceiveAvg = formatNormalValue(item.items.bitsReceivedTrend.value_avg);
  return item
}

export const changeBytesToNormalData = (limit) => {
  let size = ''
  limit = Number(limit)
  if (limit < 1000) {
    size = isInteger(limit) + ' bps'
  } else if (limit < 1000 * 1000) {
    size = isInteger(limit / 1000) + ' Kbps'
  } else if (limit < 1000 * 1000 * 1000) {
    size = isInteger(limit / (1000 * 1000)) + ' Mbps'
  } else if (limit < 1000 * 1000 * 1000 * 1000) {
    size = isInteger(limit / (1000 * 1000 * 1000)) + ' Gbps'
  } else if (limit < 1000 * 1000 * 1000 * 1000 * 1000) {
    size = isInteger(limit / (1000 * 1000 * 1000 * 1000)) + ' Tbps'
  } else {
    size = isInteger(limit / (1000 * 1000 * 1000 * 1000 * 1000)) + ' Pbps'
  }

  const sizeStr = size + ''
  const index = sizeStr.indexOf('.')
  const dou = sizeStr.substring(index + 1, 2)
  if (dou === '00') {
    return sizeStr.substring(0, index) + sizeStr.substring(index + 3, 2)
  }
  return size
}
export const timeToDateString = (longTime) => {
  let date = new Date(longTime);
  let year = date.getFullYear(),
    month = date.getMonth() + 1,
    day = date.getDate(),
    hour = date.getHours(),
    minute = date.getMinutes(),
    second = date.getSeconds();
  if (month <= 9) month = "0" + month;
  if (day <= 9) day = "0" + day;
  if (hour <= 9) hour = "0" + hour;
  if (minute <= 9) minute = "0" + minute;
  if (second <= 9) second = "0" + second;
  return (
    year + "-" + month + "-" + day + " " + hour + ":" + minute + ":" + second
  );
};

export const changeByteToNormalData = (bytes) => {
  if (bytes == 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
  // 如果小于 1KB，直接显示为 B
  if (bytes < 1) {
    return bytes + ' B';
  }
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
