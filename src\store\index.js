/*
 * @Author: tianrui.li <EMAIL>
 * @Date: 2023-02-20 11:49:35
 * @LastEditors: tianrui.li <EMAIL>
 * @LastEditTime: 2023-03-03 13:56:36
 * @FilePath: \vueDemo\src\store\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/*
 * @Author: tianrui.li <EMAIL>
 * @Date: 2023-02-20 11:49:35
 * @LastEditors: tianrui.li <EMAIL>
 * @LastEditTime: 2023-03-03 11:45:41
 * @FilePath: \vueDemo\src\store\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import Vue from 'vue'
import Vuex from 'vuex'
import tab from './tab'
// import app from './modules/app'
// import dict from './modules/dict'
// import user from './modules/user'
// import tagsView from './modules/tagsView'
// import permission from './modules/permission'
// import settings from './modules/settings'
import getters from './getters'

Vue.use(Vuex)
// 创建vuex实例
const store = new Vuex.Store({
  modules: {
    // app,
    // dict,
    // user,
    // tagsView,
    // permission,
    // settings,
    tab
  },
  getters
})

export default store
