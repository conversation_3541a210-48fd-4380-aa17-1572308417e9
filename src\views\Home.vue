<!--
 * @Author: tianrui.li <EMAIL>
 * @Date: 2023-02-21 15:53:27
 * @LastEditors: tianrui.li <EMAIL>
 * @LastEditTime: 2023-04-12 16:32:45
 * @FilePath: \vueDemo\src\views\Home.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div>
      <el-row :gutter="15">
        <el-col :span="8">
          <el-card class="box-card">
            <div class="user">
              <img src="../assets/images/user.png" alt="">
              <div class="userinfo">
                <p class="name">Admin</p>
                <p class="access">超级管理员</p>
              </div>
            </div>
            <el-divider></el-divider>
            <div class="login-info">
              <p>上次登录时间:<span>2021-7-19</span></p>
              <p>上次登录地点:<span>北京</span></p>
            </div>
          </el-card>
          <el-card style="margin-top: 20px;height:460px">
            <el-table :data="tableData" style="width:100%" border>
            <el-table-column v-for="(val, key) in tableLabel" :label="val" :prop="key" :key="key"/>
              <!-- <el-table-column label="今日购买" prop="todayBuy"></el-table-column>
              <el-table-column label="本月购买" prop="monthBuy"></el-table-column>
              <el-table-column label="总购买" prop="totalBuy"></el-table-column> -->
          </el-table>
          </el-card>
        </el-col>
        <el-col :span="16">
          <div class="num">
            <el-card v-for="item in countData" :key="item.name" :body-style="{ display:'flex', padding: '0' }">
              <i class="icon" :class="`el-icon-${item.icon}`" :style="{background: item.color}"></i>
              <div class="detail">
                <p class="price">￥{{ item.value }}</p>
                <p class="desc">{{  item.name }}</p>
              </div>
            </el-card>
          </div>
          <el-card style="height:280px">
            <!-- 折线图 -->
            <div style="height: 280px" ref="echarts1"></div>
          </el-card>
          <div class="graph">
            <el-card style="height: 260px">
              <div ref="echarts2" style="height: 260px"></div>
            </el-card>
            <!-- 饼状图 -->
            <el-card style="height: 260px">
              <div ref="echarts3" style="height:240px"></div>
            </el-card>
          </div>
        </el-col>
      </el-row>
    </div>
</template>
<script>
import {getData} from '../api/index'
import * as echarts from 'echarts'
export default{
  mounted () {
    getData().then(({data}) => {
      const { tableData } = data.data
      this.tableData = tableData

      // 基于准备好的dom，初始化echarts实例
      const echarts1 = echarts.init(this.$refs.echarts1)
      // 指定图表的配置项和数据
      var echarts1Option = {}
      // 处理数据xAxis
      const { orderData, userData, videoData } = data.data
      const xAxis = Object.keys(orderData.data[0])
      const xAxisData = {
        data: xAxis
      }
      echarts1Option.xAxis = xAxisData
      echarts1Option.yAxis = {}
      echarts1Option.tooltip = {}
      echarts1Option.legend = xAxisData
      echarts1Option.series = []
      xAxis.forEach(key => {
        echarts1Option.series.push({
          name: key,
          data: orderData.data.map(item => item[key]),
          type: 'line'
        })
      })
      echarts1.setOption(echarts1Option)

      // 柱状图1
      const echarts2 = echarts.init(this.$refs.echarts2)
      const echarts2Options = {
        legend: {
          // 图例文字颜色
          textStyle: {
            color: '#333'
          }
        },
        grid: {
          left: '20%'
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: userData.map(item => item.date),
          axisLine: {
            lineStyle: {
              color: '#17b3a3'
            }
          },
          axisLabel: {
            interval: 0,
            color: '#333'
          }
        },
        yAxis: [
          {
            type: 'value',
            axisLine: {
              lineStyle: {
                color: '#17b3a3'
              }
            }
          }
        ],
        color: ['#2ec7c9', '#b6a2de', '#5ab1ef', '#ffb980', '#d87a80', '#8d98b3'],
        series: [
          {
            name: '新增用户',
            data: userData.map(item => item.new),
            type: 'bar'
          },
          {
            name: '活跃用户',
            data: userData.map(item => item.active),
            type: 'bar'
          }
        ]
      }
      echarts2.setOption(echarts2Options)
      // 饼状图
      const echarts3 = echarts.init(this.$refs.echarts3)
      const echarts3Options = {
        tooltip: {
          trigger: 'item'
        },
        color: [
          '#0f78f4',
          '#dd536b',
          '#9462e5',
          '#a6a6a6',
          '#e1bb22',
          '#39c362',
          '#3ed1cf'
        ],
        series: [
          {
            data: videoData,
            type: 'pie'
          }
        ]
      }
      echarts3.setOption(echarts3Options)
    })
  },
  data () {
    return {
      tableData: [],
      tableLabel: {
        name: '课程',
        todayBuy: '今日购买',
        monthBuy: '本月购买',
        totalBuy: '总购买'
      },
      countData: [
        {
          name: '今日支付订单',
          value: 1234,
          icon: 'success',
          color: '#2ec7c9'
        },
        {
          name: '今日收藏订单',
          value: 210,
          icon: 'star-on',
          color: '#ffb980'
        },
        {
          name: '今日未支付订单',
          value: 1234,
          icon: 's-goods',
          color: '#5ab1ef'
        },
        {
          name: '本月支付订单',
          value: 1234,
          icon: 'success',
          color: '#2ec7c9'
        },
        {
          name: '本月收藏订单',
          value: 210,
          icon: 'star-on',
          color: '#ffb980'
        },
        {
          name: '本月未支付订单',
          value: 1234,
          icon: 's-goods',
          color: '#5ab1ef'
        }
      ]
    }
  }
}
</script>
<style scoped>
.user{
  display: flex;
  align-items: center;
}
.user img {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  margin-right: 40px;
}
.userinfo .name{
  font-size: 32px;
  margin-bottom: 10px;
}
.userinfo .access{
  color: #999999;
}
.login-info p{
  line-height: 28px;
  font-size: 14px;
  color: #999999;
}
.login-info p span{
  margin-left: 60px;
  color: #666666;
}
.num{
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.num .icon {
  width: 80px;
  height: 80px;
  font-size: 30px;
  text-align: center;
  line-height: 80px;
  color: #fff;
}

.num .detail{
  display: flex;
  /* 设置主轴方向为上下 */
  flex-direction: column;
  justify-content: center;
  margin-left: 15px;
}
.num .detail .price{
  font-size: 30px;
  margin-bottom: 10px;
  line-height: 30px;
  height: 30px;
}
.num .detail .desc {
  font-size: 14px;
  color: #999;
  text-align: center;
}
.num .el-card {
  width: 32%;
  margin-bottom: 20px;
}
.graph {
  display :flex;
  justify-content: space-between;
  margin-top:27px
}
.graph .el-card {
  width: 49%;
}
</style>
