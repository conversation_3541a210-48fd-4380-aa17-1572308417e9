/*
 * @Author: tianrui.li <EMAIL>
 * @Date: 2023-05-16 09:47:35
 * @LastEditors: tianrui.li <EMAIL>
 * @LastEditTime: 2023-05-19 11:31:53
 * @FilePath: \openevent-ui\src\api\businessmonitor.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@/utils/request'

// 查询采集数据库信息列表
export function getDataBaseList (data) {
  return request({
    url: '/db/databases/list',
    method: 'post',
    data: data
  })
};
// 新增保存采集数据库信息
export function addDataBaseList (data) {
  return request({
    url: '/db/databases/add',
    method: 'post',
    data: data
  })
};

//   修改保存采集数据库信息
export function editDataBaseList (data) {
  return request({
    url: '/db/databases/edit',
    method: 'post',
    data: data
  })
};
// 批量删除采集数据库信息
export function deleteDataBaseList (query) {
  return request({
    url: '/db/databases/remove',
    method: 'post',
    params: query
  })
};
// 删除数据库信息
export function deleteDatabasesById (data) {
  return request({
    url: '/db/databases/deleteDatabasesById',
    method: 'del',
    data: data
  })
};

// 任务管理
// 查询定时任务接口
export function listJob (data) {
  return request({
    url: '/db/task/list',
    method: 'post',
    data: data
  })
}
// 新增定时任务调度
export function addJob (data) {
  return request({
    url: '/monitor/job',
    method: 'post',
    data: data
  })
}
// 修改定时任务调度
export function updateJob (data) {
  return request({
    url: '/monitor/job',
    method: 'put',
    data: data
  })
}
// 删除定时任务调度
export function delJob (jobId) {
  return request({
    url: '/monitor/job/' + jobId,
    method: 'delete'
  })
}
// 查询定时任务调度详细
export function getJob (jobId) {
  return request({
    url: '/monitor/job/' + jobId,
    method: 'get'
  })
}
