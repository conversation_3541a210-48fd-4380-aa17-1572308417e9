export default {
  //时间选择框
  pickerOptions: {
    shortcuts: [{
        text: "最近15分钟",
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 15 * 60 * 1000);
          picker.$emit("pick", [start, end]);
        },
      },
      {
        text: "最近30分钟",
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 30 * 60 * 1000);
          picker.$emit("pick", [start, end]);
        },
      },
      {
        text: "最近1小时",
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000);
          picker.$emit("pick", [start, end]);
        },
      },
      {
        text: "最近6小时",
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 6 * 3600 * 1000);
          picker.$emit("pick", [start, end]);
        },
      },
      {
        text: "最近12小时",
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 12 * 3600 * 1000);
          picker.$emit("pick", [start, end]);
        },
      },
      {
        text: "最近24小时",
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 24 * 3600 * 1000);
          picker.$emit("pick", [start, end]);
        },
      },
      {
        text: "最近一周",
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
          picker.$emit("pick", [start, end]);
        },
      },
      {
        text: "最近一个月",
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
          picker.$emit("pick", [start, end]);
        },
      },
      {
        text: "最近三个月",
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
          picker.$emit("pick", [start, end]);
        },
      },
    ],
  },
}
