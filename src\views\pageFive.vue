<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="mini" :inline="true" v-show="showSearch" @submit.native.prevent>
      <el-form-item label="时间">
        <el-date-picker v-model="value1" type="datetimerange" :picker-options="pickerOptions" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" size="mini" @change="change" align="right">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="CPU使用率最大值">
        <el-input-number
          v-model="queryParams.cpuMax"
          :min="1"
          :max="100"
          label="label"
        ></el-input-number>
      </el-form-item>
      <el-form-item label="内存使用率最大值">
        <el-input-number
          v-model="queryParams.memMax"
          :min="1"
          :max="100"
          label="label"
        ></el-input-number>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
      <el-form-item style="float: right;">
        <download-excel class="export-excel-wrapper" :data="exportData" :fields="jsonFields" :header="title" name="性能分析.xls">
          <el-button :disabled="!Object.keys(exportData).length" icon="el-icon-download" size="mini" ></el-button>
        </download-excel>
      </el-form-item>
    </el-form>

   <el-table v-loading="loading" size="mini" @sort-change="performanceSort" :data="tableData.slice((queryParams.pageNum - 1) * queryParams.pageSize,queryParams.pageNum * queryParams.pageSize)">
      <el-table-column align="center" label="主机名" prop="host" show-overflow-tooltip></el-table-column>
      <el-table-column align="center" label="CPU数量" prop="cpuNum" sortable="custom"></el-table-column>
      <el-table-column label="CPU使用率" align="center">
        <el-table-column align="center" label="最小" prop="cpuUsedMinFormat" sortable="custom">
          <template slot-scope="scope">{{ scope.row.cpuUsedMinFormat + "%" }}</template>
        </el-table-column>
        <el-table-column align="center" label="平均" prop="cpuUsedAvgFormat" sortable="custom">
          <template slot-scope="scope">{{ scope.row.cpuUsedAvgFormat + "%" }}</template>
        </el-table-column>
        <el-table-column align="center" label="最大" prop="cpuUsedMaxFormat" sortable="custom">
          <template slot-scope="scope">{{ scope.row.cpuUsedMaxFormat + "%" }}</template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="内存大小" prop="memory" align="center" sortable="custom">
        <template slot-scope="scope">{{ scope.row.memory1 }}</template>
      </el-table-column>
      <el-table-column label="内存使用率" align="center">
        <el-table-column align="center" label="最小" prop="memoryUsedMinFormat" sortable="custom">
          <template slot-scope="scope">{{scope.row.memoryUsedMinFormat + "%"}}</template>
        </el-table-column>
        <el-table-column align="center" label="平均" prop="memoryUsedAvgFormat" sortable="custom">
          <template slot-scope="scope">{{scope.row.memoryUsedAvgFormat + "%"}}</template>
        </el-table-column>
        <el-table-column align="center" label="最大" prop="memoryUsedMaxFormat" sortable="custom">
          <template slot-scope="scope">{{scope.row.memoryUsedMaxFormat + "%"}}</template>
        </el-table-column>
      </el-table-column>
      <el-table-column align="center" label="更新时间" prop="updateTime" show-overflow-tooltip>
        <template slot-scope="{row}">{{ timeToDateString(row.updateTime*1000) }}</template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :autoScroll="false"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
    />
  </div>
</template>
<script>
import { processPerformanceItem,timeToDateString,changeByteToNormalData,changeBytesToNormalData,startTime,isNull } from "@/utils/util";
import config from '@/utils/config'
export default {
  name: "Performance",
  data() {
    return {
      // hostGroups: [],
      tableData: [],
      // hostGroupsLoading:false,
      loading: true,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        cpuMax: 10,
        memMax: 15,
      },
      listData: [],
      total: 0,
      value1: [this.$moment().subtract(1,"days"),this.$moment()],
      pickerOptions: config.pickerOptions,
      exportData: [],
      title: "性能分析",
      jsonFields: {
        主机名: "host",
        应用组: "app_name",
        IP: "ip",
        CPU数量: "cpuNum",
        'CPU使用率(最小)': "cpuUsedMinFormat",
        'CPU使用率(平均)': "cpuUsedAvgFormat",
        'CPU使用率(最大)': "cpuUsedMaxFormat",
        内存大小: "memory",
        '内存使用率(最小)': "memoryUsedMinFormat",
        '内存使用率(平均)': "memoryUsedAvgFormat",
        '内存使用率{最大}': "memoryUsedMaxFormat",
        更新时间: "updateTime",
      },
      groupName:"",
      peakValue: 80
    };
  },
  created() {
    // this.loadData()
  },
  methods: {
    timeToDateString,
    changeBytesToNormalData,
    // 查询性能分析数据
    async loadData(){
      try{
        this.loading = true
        this.exportData = []
        const [startTime, endTime] = this.value1
        const formData = new FormData()
        formData.append("startTime",this.$moment(startTime).format("X"))
        formData.append("endTime",this.$moment(endTime).format("X"))
        const res = await getOSPerformanceReport(formData)
        this.loading = false
        this.tableData = res.data ? res.data : [];
        this.tableData.forEach((item) => {
          processPerformanceItem(item)
        })
        this.listData = this.tableData
        this.exportData = this.tableData.map(item => {
          return{
            ...item,
            cpuUsedMinFormat: `${item.cpuUsedMinFormat}%`,
            cpuUsedAvgFormat: `${item.cpuUsedAvgFormat}%`,
            cpuUsedMaxFormat: `${item.cpuUsedMaxFormat}%`,
            memoryUsedMinFormat: `${item.memoryUsedMinFormat}%`,
            memoryUsedAvgFormat: `${item.memoryUsedAvgFormat}%`,
            memoryUsedMaxFormat: `${item.memoryUsedMaxFormat}%`,
            systemInfo: item.systemInfo || "-",
            memory: item.memory1 || "-",
            updateTime1: timeToDateString(item.updateTime * 1000),
          }
        })
      }catch(error){
        console.error('Error loading data:', error)
        this.loading = false
      }
    },

    changeByteToNormalData,
    //重置按钮操作
    resetQuery() {
      this.resetForm("queryForm");
      this.loading = true
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        cpuMax: '',
        memMax: '',
      },
      this.getGroupData();
    },
    change(avlue){
      this.loadData()
    },
    handleQuery() {
      const cpuMax = parseFloat(this.queryParams.cpuMax) || Infinity;
      const memMax = parseFloat(this.queryParams.memMax) || Infinity;

      this.tableData = [...this.listData].filter(item => {
        const cpuCondition = item.cpuUsedMaxFormat <= cpuMax;
        const memCondition = item.memoryUsedMaxFormat <= memMax;
        return cpuCondition && memCondition
      });
      this.total = this.tableData.length;
    },
    performanceSort({column,prop,order}){
      if(order === 'descending'){
        this.tableData = [...this.tableData].sort((a,b) => b[prop] - a[prop])
      } else {
        this.tableData = [...this.tableData].sort((a,b) => a[prop] - b[prop])
      }
    },
  },
};
</script>

<style scoped>
.el-tooltip__popper {
  font-size: 14px;
  max-width: 20%;
}
::v-deep .oneLine {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
