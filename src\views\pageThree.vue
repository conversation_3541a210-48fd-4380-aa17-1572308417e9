<!--
 * @Author: tianrui.li <EMAIL>
 * @Date: 2023-06-21 14:32:53
 * @LastEditors: tianrui.li <EMAIL>
 * @LastEditTime: 2023-08-11 11:48:39
 * @FilePath: \vueDemo\src\views\pageThree.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div>
        <!-- <el-form :model="form" ref="form" size="mini" :inline="true">
            <el-form-item label="标记" prop="tag">
                <el-select v-model="form.tag" multiple placeholder="请选择">
                    <el-option v-for="(item, index) in tagArr" :key="index" :label="item" :value="item"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button @click="handleSearch">搜索</el-button>
            </el-form-item>
        </el-form>
        <el-table :data="tableData" border>
            <el-table-column label="名称" prop="name"></el-table-column>
            <el-table-column label="属性" prop="property"></el-table-column>
            <el-table-column label="标记" show-overflow-tooltip>
                <template slot-scope="scope">
                    <el-tag style="margin-right:5px" v-for="item in scope.row.tags" :key="item.name">{{ item.tag + ':' +
                        item.value }}</el-tag>
                </template>
            </el-table-column>
        </el-table> -->
        <section>
            <div class="card">
                <div class="box">
                    <div class="img-box">
                        <img src="../assets/images/user.png" alt="">
                    </div>
                    <div class="con-box">
                        <div>
                            <h2>蒙奇·D·路飞</h2>
                            <p>蒙奇·D·路飞，日本漫画《航海王》及其衍生作品中的男主角。外号“草帽”路飞，是草帽一伙、草帽大船团的船长，极恶的世代之一。橡胶果实能力者，悬赏金15亿贝里。梦想是找到传说中的One
                                Piece，成为海贼王。</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card">
                <div class="box">
                    <div class="img-box">
                        <img src="../assets/images/user.png" alt="">
                    </div>
                    <div class="con-box">
                        <div>
                            <h2>罗罗诺亚·索隆</h2>
                            <p>罗罗诺亚・索隆，日本漫画《航海王》及其衍生作品中的角色。“草帽一伙”的战斗员，人称“海贼猎人”。2年前登陆香波地群岛的11位“超新星”其中的一位。同时也是被人称作“极恶的世代”中的一位。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card">
                <div class="box">
                    <div class="img-box">
                        <img src="../assets/images/user.png" alt="">
                    </div>
                    <div class="con-box">
                        <div>
                            <h2>山治</h2>
                            <p>山治，日本漫画《海贼王》及其衍生作品中的角色。草帽一伙的厨师，因踢技快准狠被海军称之为“黑足”，悬赏金为3亿3000万贝里。海贼中的绅士，有着卷曲眉毛，永远遮住半边脸的家伙，香烟不离口，最爱女人，很花心但很有风度，从不愿意伤害任何女性，哪怕是敌人。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
</template>
<script>
export default {
    data() {
        return {
            tableData: [
                {
                    name: '[Drive_0_0_1]drive lifecycle_state',
                    property: 'property1',
                    tags: [
                        { tag: 'Application', value: 'Health' },
                        { tag: 'component', value: 'health' },
                        { tag: "Application", value: "Drive" },
                        { tag: "Application", value: "FAN" },
                        { tag: "Application", value: "Port" }
                    ]
                },
                {
                    name: '[Drive_0_0_0]drive drive_type',
                    property: 'property1',
                    tags: [
                        { tag: 'component', value: 'health' },
                        { tag: 'Application', value: 'Health' },
                    ]
                },
                {
                    name: '[Drive_0_0_0]drive firmware_version',
                    property: 'property1',
                    tags: [
                        { tag: "Application", value: "Drive" },
                        { tag: "Application", value: "Port" }
                    ]
                }
            ],
            form: {},
            tagArr: []
        }
    },
    created() {
        this.getList()
        this.getData()
    },
    methods: {
        getList() {
            this.tableData.forEach((item) => {
                item.tags.forEach((obj) => {
                    this.tagArr.push(obj.tag + ':' + obj.value)
                })
                const rent = []
                rent.length = 0
                item.tags.forEach((item_tag) => {
                    rent.push(item_tag.tag + ':' + item_tag.value)
                })
                item.rent = rent
            })
            this.tagArr = Array.from(new Set(this.tagArr))
        },
        handleSearch() {
            this.tableData = this.tableData.filter((item) => {
                return (
                    this.getStringData(item.rent, this.form.tag)
                )
            })
        },
        // item.rent,form.tag
        getStringData(a, b) {
            return b.every(item => {
                return a.some(v => {
                    return item == v
                })
            })
        },
        getData() {
            const list = {
                12: [
                    {
                        id: 1,
                        typeOrder: 5,
                    },
                    {
                        id: 2,
                        typeOrder: 5,
                    },
                ],
                34: [
                    {
                        id: 3,
                        typeOrder: 2,
                    },
                    {
                        id: 4,
                        typeOrder: 2,
                    },
                    {
                        id: 5,
                        typeOrder: 2,
                    },
                ],
                56: [
                    {
                        id: 6,
                        typeOrder: 1,
                    },
                    {
                        id: 7,
                        typeOrder: 1,
                    },
                    {
                        id: 8,
                        typeOrder: 1,
                    },
                ],
            };

            // 对象转为数组，并按照 typeOrder 属性进行排序
            const sortedList = Object.entries(list).map(([key, value]) => {
                return {
                    key,
                    value: value.sort((a, b) => a.typeOrder - b.typeOrder),
                };
            });

            // 根据 key 进行排序（可选）
            sortedList.sort((a, b) => a.key - b.key);

            // 打印结果
            console.log(sortedList);
        }
    }
}
</script>
<style scoped>
* {
    /* 初始化 */
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    /* 100%窗口高度 */
    min-height: 100vh;
    /* 弹性布局 水平+垂直居中 */
    display: flex;
    justify-content: center;
    align-items: center;
    background: linear-gradient(200deg, #fff1eb, #ace0f9);
}

section {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
}

.card {
    /* 相对定位 */
    position: relative;
    width: 320px;
    height: 320px;
    margin: 20px;
    /* 开启3D效果 */
    transform-style: preserve-3d;
    perspective: 1000px;
}

.card .box {
    /* 绝对定位 */
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    transform-style: preserve-3d;
    /* 设置过渡 */
    transition: 1s ease;
}

.card .box .img-box {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.card .box .img-box img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.card .box .con-box {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #333;
    display: flex;
    justify-content: center;
    align-items: center;
    /* 隐藏旋转div元素的背面 */
    backface-visibility: hidden;
    transform-style: preserve-3d;
    /* 默认沿Y轴旋转180度 */
    transform: rotateY(180deg);
}

.card .box .con-box div {
    transform-style: preserve-3d;
    padding: 20px;
    color: #fff;
    /* 渐变背景 */
    background: linear-gradient(30deg, #514a9d, #24c6dc);
    /* 默认沿Z轴偏移100px */
    transform: translateZ(100px);
}

.card .box .con-box div h2 {
    font-size: 20px;
    letter-spacing: 1px;
}

.card .box .con-box div p {
    margin-top: 5px;
    font-size: 15px;
    text-align: justify;
}

.card:hover .box {
    /* 鼠标移入，盒子旋转180度 */
    transform: rotateY(180deg);
}
</style>